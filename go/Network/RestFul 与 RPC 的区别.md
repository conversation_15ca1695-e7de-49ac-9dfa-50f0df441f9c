## RESTful 与 RPC 的区别

### 核心对比

| 特性 | RESTful | RPC |
|------|---------|-----|
| **设计理念** | 以资源为中心 | 以方法为中心 |
| **协议** | 基于HTTP | 可用多种协议(HTTP/TCP/UDP) |
| **接口风格** | 统一的HTTP方法 | 自定义方法名 |
| **数据格式** | JSON/XML | JSON/Protobuf/Thrift |
| **状态** | 无状态 | 可有状态 |
| **缓存** | 天然支持HTTP缓存 | 需要自己实现 |
| **学习成本** | 低(基于HTTP) | 中等(需要学习特定协议) |

### 设计风格差异

#### RESTful风格
```
GET    /users/123        # 获取用户
POST   /users            # 创建用户
PUT    /users/123        # 更新用户
DELETE /users/123        # 删除用户
```

#### RPC风格
```
getUserById(123)         # 获取用户
createUser(userData)     # 创建用户
updateUser(123, data)    # 更新用户
deleteUser(123)          # 删除用户
```

### 适用场景

#### RESTful适用场景
- **Web API**：面向公网的API服务
- **移动应用**：客户端与服务端通信
- **微服务**：服务间的HTTP通信
- **CRUD操作**：简单的增删改查

#### RPC适用场景
- **内部服务**：微服务间的高性能通信
- **实时系统**：对延迟要求极高的场景
- **复杂业务**：需要传递复杂参数的方法调用
- **跨语言调用**：不同语言服务间的通信

### 性能对比

**RESTful**：
- 优点：简单、标准化、易调试
- 缺点：HTTP开销大、序列化效率低

**RPC**：
- 优点：性能高、二进制协议、类型安全
- 缺点：学习成本高、调试困难

### 主流RPC框架

- **gRPC**：Google开发，基于HTTP/2和Protobuf
- **Thrift**：Facebook开发，支持多种语言
- **Dubbo**：阿里开发，Java生态系统
- **JSON-RPC**：轻量级，基于JSON

### 面试要点

**Q: 什么时候选择RESTful，什么时候选择RPC？**
A:
- **RESTful**：对外API、Web应用、简单CRUD
- **RPC**：内部服务、高性能要求、复杂业务逻辑

**Q: gRPC相比RESTful有什么优势？**
A:
1. 性能更高（HTTP/2、二进制协议）
2. 类型安全（Protobuf强类型）
3. 支持流式传输
4. 自动生成客户端代码

**Q: RESTful的幂等性如何保证？**
A:
- GET、PUT、DELETE天然幂等
- POST非幂等，可通过业务逻辑保证
- 使用唯一标识符避免重复操作