## TCP与UDP协议对比

### 核心区别

| 特性 | TCP | UDP |
|------|-----|-----|
| **连接方式** | 面向连接（三次握手） | 无连接 |
| **可靠性** | 可靠传输，保证顺序 | 不保证可靠性 |
| **传输方式** | 字节流 | 数据报 |
| **速度** | 较慢（有确认机制） | 较快（无确认机制） |
| **开销** | 大（维护连接状态） | 小（无连接状态） |
| **头部大小** | 20字节 | 8字节 |
| **流量控制** | 有（滑动窗口） | 无 |
| **拥塞控制** | 有 | 无 |

### 应用场景

**TCP适用场景**：
- HTTP/HTTPS（Web浏览）
- FTP（文件传输）
- SMTP（邮件发送）
- SSH（远程登录）
- 数据库连接

**UDP适用场景**：
- DNS（域名解析）
- DHCP（动态主机配置）
- 视频直播
- 在线游戏
- 语音通话

### TCP连接管理

**三次握手建立连接**：
1. **SYN** → 客户端请求连接（发送序列号）
2. **SYN+ACK** → 服务端确认并请求（发送序列号+确认号）
3. **ACK** → 客户端确认，连接建立

**四次挥手关闭连接**：
1. **FIN** → 主动方请求关闭
2. **ACK** → 被动方确认（可能还有数据传输）
3. **FIN** → 被动方请求关闭
4. **ACK** → 主动方确认，进入TIME_WAIT

### 关键问题解答

**Q: 为什么是三次握手而不是两次？**
A: 防止旧的重复连接请求导致错误连接，确保双方都确认对方的收发能力

**Q: 为什么是四次挥手而不是三次？**
A: TCP是全双工通信，需要分别关闭两个方向的数据传输

**Q: TIME_WAIT状态的作用？**
A: 确保最后的ACK能够到达对方，防止旧连接的数据包干扰新连接

**Q: TCP如何保证可靠性？**
A: 序列号、确认应答、超时重传、流量控制、拥塞控制

### 面试要点

1. **连接建立**：三次握手的必要性和过程
2. **连接释放**：四次挥手的原因和TIME_WAIT状态
3. **可靠性机制**：重传、流量控制、拥塞控制
4. **性能对比**：TCP vs UDP的选择依据