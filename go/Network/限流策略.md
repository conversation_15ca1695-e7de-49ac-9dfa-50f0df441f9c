## 限流策略与实现

### 限流的作用

限流是保护系统资源、防止服务过载的重要手段，通过控制请求速率确保系统稳定性。

#### 限流目标
- **保护系统**：防止过载导致服务崩溃
- **保证SLA**：维持服务质量和响应时间
- **公平使用**：防止某些用户占用过多资源
- **成本控制**：控制资源使用成本

### 常见限流算法

#### 1. 固定窗口限流

**原理**：在固定时间窗口内限制请求数量

```go
type FixedWindowLimiter struct {
    limit    int
    window   time.Duration
    counter  int
    lastTime time.Time
    mutex    sync.Mutex
}

func NewFixedWindowLimiter(limit int, window time.Duration) *FixedWindowLimiter {
    return &FixedWindowLimiter{
        limit:    limit,
        window:   window,
        lastTime: time.Now(),
    }
}

func (f *FixedWindowLimiter) Allow() bool {
    f.mutex.Lock()
    defer f.mutex.Unlock()
    
    now := time.Now()
    if now.Sub(f.lastTime) >= f.window {
        f.counter = 0
        f.lastTime = now
    }
    
    if f.counter >= f.limit {
        return false
    }
    
    f.counter++
    return true
}
```

**特点**：
- ✅ 实现简单，内存占用小
- ❌ 窗口边界可能出现流量突增

#### 2. 令牌桶算法

**原理**：以固定速率生成令牌，请求消耗令牌，允许突发流量

```go
import "golang.org/x/time/rate"

// 使用标准库实现
func tokenBucketExample() {
    // 每秒生成10个令牌，桶容量20
    limiter := rate.NewLimiter(rate.Limit(10), 20)
    
    // 检查是否允许请求
    if limiter.Allow() {
        fmt.Println("Request allowed")
    } else {
        fmt.Println("Request denied")
    }
    
    // 等待直到有令牌可用
    ctx := context.Background()
    err := limiter.Wait(ctx)
    if err != nil {
        fmt.Println("Request cancelled")
    }
}
```

**特点**：
- ✅ 允许突发流量
- ✅ 平均速率控制
- ❌ 可能出现令牌消耗过快

#### 3. 漏桶算法

**原理**：请求进入桶中，以固定速率处理，严格控制输出速率

**特点**：
- ✅ 严格控制输出速率
- ✅ 平滑处理请求
- ❌ 不允许突发流量

### 分布式限流

#### 基于Redis的分布式限流

```go
import (
    "github.com/go-redis/redis/v8"
    "context"
    "time"
)

type RedisRateLimiter struct {
    client *redis.Client
    script string
}

func NewRedisRateLimiter(client *redis.Client) *RedisRateLimiter {
    // Lua脚本实现原子操作
    script := `
        local key = KEYS[1]
        local limit = tonumber(ARGV[1])
        local window = tonumber(ARGV[2])
        local current = redis.call('GET', key)
        
        if current == false then
            redis.call('SET', key, 1)
            redis.call('EXPIRE', key, window)
            return 1
        end
        
        current = tonumber(current)
        if current < limit then
            redis.call('INCR', key)
            return current + 1
        else
            return -1
        end
    `
    
    return &RedisRateLimiter{
        client: client,
        script: script,
    }
}

func (r *RedisRateLimiter) Allow(ctx context.Context, key string, limit int, window time.Duration) bool {
    result, err := r.client.Eval(ctx, r.script, []string{key}, limit, int(window.Seconds())).Result()
    if err != nil {
        return false
    }
    
    count, ok := result.(int64)
    return ok && count > 0
}
```

### 限流中间件实现

#### HTTP中间件

```go
func RateLimitMiddleware(limiter *rate.Limiter) func(http.Handler) http.Handler {
    return func(next http.Handler) http.Handler {
        return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            if !limiter.Allow() {
                http.Error(w, "Too Many Requests", http.StatusTooManyRequests)
                return
            }
            next.ServeHTTP(w, r)
        })
    }
}
```

### 限流策略选择

#### 算法对比

| 算法 | 突发流量 | 实现复杂度 | 内存占用 | 适用场景 |
|------|----------|------------|----------|----------|
| **固定窗口** | 部分支持 | 低 | 低 | 简单场景 |
| **滑动窗口** | 较好支持 | 中 | 中 | 平滑限流 |
| **令牌桶** | 很好支持 | 中 | 低 | 突发流量 |
| **漏桶** | 不支持 | 中 | 中 | 严格限速 |

#### 选择建议

**API网关限流**：
- 使用令牌桶算法
- 支持突发流量
- 配置灵活

**数据库保护**：
- 使用漏桶算法
- 严格控制并发
- 保护后端资源

**用户行为限制**：
- 使用滑动窗口
- 防止恶意刷量
- 用户体验好

### 面试要点

**Q: 令牌桶和漏桶的区别？**
A:
- **令牌桶**：允许突发流量，令牌生成速率固定
- **漏桶**：严格限制输出速率，不允许突发

**Q: 如何实现分布式限流？**
A:
1. **Redis计数器**：使用Redis存储计数
2. **Lua脚本**：保证操作原子性
3. **滑动窗口**：Redis有序集合实现
4. **令牌桶**：Redis+定时任务补充令牌

**Q: 限流失效后如何处理？**
A:
1. **降级策略**：返回默认值或缓存数据
2. **排队等待**：将请求放入队列延迟处理
3. **拒绝服务**：直接返回429状态码
4. **熔断机制**：暂时停止服务调用

**Q: 如何监控限流效果？**
A:
1. **限流命中率**：被限流的请求比例
2. **响应时间**：限流对性能的影响
3. **错误率**：限流导致的错误增加
4. **业务指标**：对核心业务的影响
