## WebSocket协议

### WebSocket概述

WebSocket是一种全双工通信协议，基于TCP连接，允许客户端和服务器之间建立持久连接进行实时双向数据传输。

### WebSocket vs HTTP

| 特性 | HTTP | WebSocket |
|------|------|-----------|
| **通信模式** | 请求-响应 | 全双工 |
| **连接方式** | 短连接 | 长连接 |
| **协议开销** | 每次请求都有头部 | 握手后开销小 |
| **实时性** | 需要轮询 | 真正实时 |
| **服务器推送** | 不支持 | 原生支持 |

### WebSocket握手过程

#### 1. 客户端发起升级请求
```http
GET /chat HTTP/1.1
Host: example.com
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==
Sec-WebSocket-Version: 13
```

#### 2. 服务器响应升级
```http
HTTP/1.1 101 Switching Protocols
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Accept: s3pPLMBiTxaQ9kYGzzhZRbK+xOo=
```

#### 3. 握手完成，开始WebSocket通信

### WebSocket帧格式

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-------+-+-------------+-------------------------------+
|F|R|R|R| opcode|M| Payload len |    Extended payload length    |
|I|S|S|S|  (4)  |A|     (7)     |             (16/64)           |
|N|V|V|V|       |S|             |   (if payload len==126/127)   |
| |1|2|3|       |K|             |                               |
+-+-+-+-+-------+-+-------------+ - - - - - - - - - - - - - - - +
|     Extended payload length continued, if payload len == 127  |
+ - - - - - - - - - - - - - - - +-------------------------------+
|                               |Masking-key, if MASK set to 1  |
+-------------------------------+-------------------------------+
| Masking-key (continued)       |          Payload Data         |
+-------------------------------- - - - - - - - - - - - - - - - +
:                     Payload Data continued ...                :
+ - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - +
|                     Payload Data continued ...                |
+---------------------------------------------------------------+
```

### 操作码类型

| 操作码 | 含义 | 说明 |
|--------|------|------|
| **0x0** | 继续帧 | 分片消息的中间帧 |
| **0x1** | 文本帧 | UTF-8文本数据 |
| **0x2** | 二进制帧 | 二进制数据 |
| **0x8** | 关闭帧 | 关闭连接 |
| **0x9** | Ping帧 | 心跳检测 |
| **0xA** | Pong帧 | 心跳响应 |

### Go语言WebSocket实现

#### 使用gorilla/websocket库
```go
package main

import (
    "log"
    "net/http"

    "github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
    CheckOrigin: func(r *http.Request) bool {
        return true // 允许跨域
    },
}

func wsHandler(w http.ResponseWriter, r *http.Request) {
    // 升级HTTP连接为WebSocket
    conn, err := upgrader.Upgrade(w, r, nil)
    if err != nil {
        log.Println("升级失败:", err)
        return
    }
    defer conn.Close()

    for {
        // 读取消息
        messageType, message, err := conn.ReadMessage()
        if err != nil {
            log.Println("读取消息失败:", err)
            break
        }

        log.Printf("收到消息: %s", message)

        // 回显消息
        err = conn.WriteMessage(messageType, message)
        if err != nil {
            log.Println("发送消息失败:", err)
            break
        }
    }
}

func main() {
    http.HandleFunc("/ws", wsHandler)
    log.Println("WebSocket服务器启动在 :8080")
    log.Fatal(http.ListenAndServe(":8080", nil))
}
```

#### WebSocket客户端
```go
package main

import (
    "log"
    "time"

    "github.com/gorilla/websocket"
)

func main() {
    // 连接WebSocket服务器
    conn, _, err := websocket.DefaultDialer.Dial("ws://localhost:8080/ws", nil)
    if err != nil {
        log.Fatal("连接失败:", err)
    }
    defer conn.Close()

    // 发送消息
    err = conn.WriteMessage(websocket.TextMessage, []byte("Hello WebSocket"))
    if err != nil {
        log.Println("发送失败:", err)
        return
    }

    // 读取响应
    _, message, err := conn.ReadMessage()
    if err != nil {
        log.Println("读取失败:", err)
        return
    }

    log.Printf("收到响应: %s", message)
}
```

### WebSocket连接管理

#### 连接池管理
```go
type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
}

type Client struct {
    hub  *Hub
    conn *websocket.Conn
    send chan []byte
}

func (h *Hub) run() {
    for {
        select {
        case client := <-h.register:
            h.clients[client] = true

        case client := <-h.unregister:
            if _, ok := h.clients[client]; ok {
                delete(h.clients, client)
                close(client.send)
            }

        case message := <-h.broadcast:
            for client := range h.clients {
                select {
                case client.send <- message:
                default:
                    close(client.send)
                    delete(h.clients, client)
                }
            }
        }
    }
}
```

### 心跳检测

#### Ping/Pong机制
```go
func (c *Client) writePump() {
    ticker := time.NewTicker(54 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case message := <-c.send:
            c.conn.WriteMessage(websocket.TextMessage, message)

        case <-ticker.C:
            // 发送ping帧
            if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
                return
            }
        }
    }
}

func (c *Client) readPump() {
    // 设置pong处理器
    c.conn.SetPongHandler(func(string) error {
        c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
        return nil
    })

    for {
        _, message, err := c.conn.ReadMessage()
        if err != nil {
            break
        }
        c.hub.broadcast <- message
    }
}
```

### 应用场景

#### 实时应用
- **即时通讯**：聊天室、私信
- **实时通知**：系统消息、状态更新
- **在线协作**：文档编辑、白板
- **实时监控**：系统监控、日志查看
- **在线游戏**：状态同步、实时对战

#### 技术选型考虑
| 场景 | 推荐方案 | 原因 |
|------|----------|------|
| **简单推送** | Server-Sent Events | 单向推送，实现简单 |
| **双向实时通信** | WebSocket | 全双工，低延迟 |
| **高频数据更新** | WebSocket | 减少HTTP开销 |
| **偶尔推送** | HTTP轮询 | 实现简单，资源占用少 |

### 性能优化

#### 1. 连接管理
- 合理设置连接超时
- 实现连接池复用
- 及时清理断开的连接

#### 2. 消息处理
- 使用消息队列缓冲
- 批量处理消息
- 压缩大消息

#### 3. 扩展性
- 使用Redis发布订阅
- 负载均衡多个WebSocket服务器
- 水平扩展

### 面试要点

**Q: WebSocket和HTTP的主要区别？**
A:
1. **连接方式**：WebSocket是长连接，HTTP是短连接
2. **通信模式**：WebSocket全双工，HTTP请求-响应
3. **协议开销**：WebSocket握手后开销小，HTTP每次都有头部
4. **实时性**：WebSocket真正实时，HTTP需要轮询

**Q: WebSocket如何保持连接？**
A:
1. **心跳检测**：定期发送Ping/Pong帧
2. **超时设置**：设置读写超时时间
3. **错误处理**：监听连接状态，及时重连

**Q: WebSocket的安全性如何保证？**
A:
1. **WSS协议**：使用TLS加密传输
2. **Origin检查**：验证请求来源
3. **身份认证**：在握手时验证用户身份
4. **消息验证**：对消息内容进行校验

**Q: 如何处理WebSocket的高并发？**
A:
1. **连接池管理**：限制单机连接数
2. **消息队列**：异步处理消息
3. **负载均衡**：分散连接到多台服务器
4. **资源监控**：监控内存和CPU使用