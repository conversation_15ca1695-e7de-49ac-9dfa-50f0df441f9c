## 常见网络攻击与防护

### Web应用攻击

#### 1. XSS（跨站脚本攻击）

**攻击原理**：在网页中注入恶意脚本，当用户访问时执行恶意代码。

##### XSS类型对比
| 类型 | 存储位置 | 触发方式 | 危害程度 |
|------|----------|----------|----------|
| **存储型XSS** | 服务器数据库 | 访问页面 | 高 |
| **反射型XSS** | URL参数 | 点击链接 | 中 |
| **DOM型XSS** | 客户端DOM | JavaScript操作 | 中 |

##### 攻击示例
```html
<!-- 存储型XSS -->
评论内容: <script>alert('XSS')</script>

<!-- 反射型XSS -->
URL: http://example.com/search?q=<script>alert('XSS')</script>

<!-- DOM型XSS -->
document.getElementById('content').innerHTML = location.hash.substr(1);
```

##### 防护措施
```go
// Go语言XSS防护
import (
    "html/template"
    "net/http"
)

func safeOutput(w http.ResponseWriter, userInput string) {
    // 1. HTML转义
    tmpl := template.Must(template.New("safe").Parse(`
        <div>{{.}}</div>
    `))
    tmpl.Execute(w, userInput)

    // 2. 手动转义
    escaped := template.HTMLEscapeString(userInput)
    w.Write([]byte(escaped))
}

// CSP头部设置
func setCSP(w http.ResponseWriter) {
    w.Header().Set("Content-Security-Policy",
        "default-src 'self'; script-src 'self' 'unsafe-inline'")
}
```

#### 2. CSRF（跨站请求伪造）

**攻击原理**：利用用户已登录状态，诱导执行非预期操作。

##### 攻击流程
```
1. 用户登录银行网站 → 获得认证Cookie
2. 访问恶意网站 → 恶意网站构造转账请求
3. 浏览器自动携带Cookie → 银行网站执行转账
4. 用户资金被盗 → 攻击成功
```

##### 防护实现
```go
// CSRF Token防护
import (
    "crypto/rand"
    "encoding/base64"
    "net/http"
)

func generateCSRFToken() string {
    b := make([]byte, 32)
    rand.Read(b)
    return base64.StdEncoding.EncodeToString(b)
}

func csrfMiddleware(next http.HandlerFunc) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        if r.Method == "POST" {
            token := r.FormValue("csrf_token")
            sessionToken := getSessionToken(r) // 从session获取

            if token != sessionToken {
                http.Error(w, "CSRF token mismatch", http.StatusForbidden)
                return
            }
        }
        next(w, r)
    }
}

// SameSite Cookie设置
func setSecureCookie(w http.ResponseWriter, name, value string) {
    cookie := &http.Cookie{
        Name:     name,
        Value:    value,
        HttpOnly: true,
        Secure:   true,
        SameSite: http.SameSiteStrictMode,
    }
    http.SetCookie(w, cookie)
}
```

#### 3. SQL注入

**攻击原理**：通过输入恶意SQL代码，操控数据库执行非预期查询。

##### 攻击示例
```sql
-- 原始查询
SELECT * FROM users WHERE username = 'admin' AND password = 'password'

-- 注入攻击
username: admin' OR '1'='1' --
-- 结果查询
SELECT * FROM users WHERE username = 'admin' OR '1'='1' --' AND password = 'password'
```

##### 防护措施
```go
// 使用参数化查询
import (
    "database/sql"
    _ "github.com/go-sql-driver/mysql"
)

func safeQuery(db *sql.DB, username, password string) (*sql.Rows, error) {
    // 错误方式：字符串拼接
    // query := fmt.Sprintf("SELECT * FROM users WHERE username = '%s' AND password = '%s'", username, password)

    // 正确方式：参数化查询
    query := "SELECT * FROM users WHERE username = ? AND password = ?"
    return db.Query(query, username, password)
}

// 输入验证
func validateInput(input string) bool {
    // 白名单验证
    matched, _ := regexp.MatchString("^[a-zA-Z0-9_]+$", input)
    return matched
}
```

### 网络层攻击

#### 1. DDoS攻击

**攻击类型**：
- **SYN Flood**：TCP半连接攻击
- **UDP Flood**：UDP数据包洪水
- **HTTP Flood**：应用层请求洪水
- **DNS放大**：利用DNS响应放大攻击

##### 防护策略
```go
// 连接限制
type ConnectionLimiter struct {
    connections map[string]int
    mutex       sync.RWMutex
    maxConn     int
}

func (cl *ConnectionLimiter) Allow(ip string) bool {
    cl.mutex.Lock()
    defer cl.mutex.Unlock()

    if cl.connections[ip] >= cl.maxConn {
        return false
    }
    cl.connections[ip]++
    return true
}

// 速率限制
import "golang.org/x/time/rate"

func rateLimitMiddleware(limiter *rate.Limiter) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        if !limiter.Allow() {
            http.Error(w, "Too Many Requests", http.StatusTooManyRequests)
            return
        }
        // 处理正常请求
    }
}
```

#### 2. 中间人攻击（MITM）

**攻击原理**：攻击者拦截并可能修改通信双方的数据。

##### 防护措施
```go
// TLS配置
import (
    "crypto/tls"
    "net/http"
)

func secureServer() {
    cfg := &tls.Config{
        MinVersion:               tls.VersionTLS12,
        CurvePreferences:         []tls.CurveID{tls.CurveP521, tls.CurveP384, tls.CurveP256},
        PreferServerCipherSuites: true,
        CipherSuites: []uint16{
            tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
            tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
            tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
        },
    }

    srv := &http.Server{
        Addr:         ":443",
        Handler:      nil,
        TLSConfig:    cfg,
        TLSNextProto: make(map[string]func(*http.Server, *tls.Conn, http.Handler), 0),
    }

    srv.ListenAndServeTLS("cert.pem", "key.pem")
}

// 证书固定
func pinCertificate(cert *x509.Certificate) bool {
    expectedFingerprint := "sha256:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="
    actualFingerprint := sha256.Sum256(cert.Raw)
    return base64.StdEncoding.EncodeToString(actualFingerprint[:]) == expectedFingerprint[7:]
}
```

### 安全最佳实践

#### 1. 输入验证
```go
// 严格的输入验证
func validateEmail(email string) bool {
    re := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
    return re.MatchString(email)
}

func sanitizeInput(input string) string {
    // 移除危险字符
    re := regexp.MustCompile(`[<>\"'%;()&+]`)
    return re.ReplaceAllString(input, "")
}
```

#### 2. 安全头部
```go
func securityHeaders(next http.HandlerFunc) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        // XSS保护
        w.Header().Set("X-XSS-Protection", "1; mode=block")
        // 内容类型嗅探保护
        w.Header().Set("X-Content-Type-Options", "nosniff")
        // 点击劫持保护
        w.Header().Set("X-Frame-Options", "DENY")
        // HTTPS强制
        w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        // CSP策略
        w.Header().Set("Content-Security-Policy", "default-src 'self'")

        next(w, r)
    }
}
```

### 面试要点

**Q: XSS和CSRF的区别？**
A:
- **XSS**：注入恶意脚本，在用户浏览器执行，窃取信息
- **CSRF**：伪造用户请求，利用用户权限执行操作

**Q: 如何防护SQL注入？**
A:
1. **参数化查询**：使用占位符而非字符串拼接
2. **输入验证**：白名单验证，过滤特殊字符
3. **最小权限**：数据库用户权限最小化
4. **WAF防护**：Web应用防火墙过滤

**Q: DDoS攻击如何防护？**
A:
1. **流量清洗**：CDN和DDoS防护服务
2. **限流策略**：IP限流、接口限流
3. **负载均衡**：分散攻击压力
4. **黑洞路由**：丢弃攻击流量

**Q: HTTPS如何防止中间人攻击？**
A:
1. **加密传输**：TLS加密所有数据
2. **证书验证**：验证服务器身份
3. **证书固定**：固定证书指纹
4. **HSTS**：强制使用HTTPS