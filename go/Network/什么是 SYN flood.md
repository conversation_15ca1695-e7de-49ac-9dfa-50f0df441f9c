## SYN Flood攻击

### 攻击原理

SYN Flood是一种利用TCP三次握手机制的DDoS攻击，通过大量伪造的SYN请求耗尽服务器资源。

#### 正常三次握手
```
客户端                    服务器
  |                        |
  |-------- SYN ---------->| 1. 请求连接
  |<----- SYN+ACK ---------| 2. 确认并请求
  |-------- ACK ---------->| 3. 确认连接建立
  |                        |
  |     正常数据传输        |
```

#### SYN Flood攻击过程
```
攻击者                    服务器
  |                        |
  |-------- SYN ---------->| 1. 伪造源IP发送SYN
  |<----- SYN+ACK ---------| 2. 服务器分配资源等待ACK
  |                        | 3. 攻击者不回复ACK
  |-------- SYN ---------->| 4. 继续发送大量SYN
  |<----- SYN+ACK ---------| 5. 服务器资源耗尽
  |                        |
  |     服务器无法响应      |
```

### 攻击特征

#### 半连接状态堆积
- **SYN_RCVD状态**：服务器等待客户端ACK
- **资源占用**：每个半连接占用内存和端口
- **队列满载**：半连接队列达到上限
- **拒绝服务**：无法处理新的连接请求

#### 攻击效果
| 影响 | 表现 | 后果 |
|------|------|------|
| **连接队列满** | 新连接被拒绝 | 用户无法访问 |
| **内存耗尽** | 系统响应缓慢 | 整体性能下降 |
| **CPU占用高** | 处理大量SYN包 | 系统负载过高 |
| **网络拥塞** | 带宽被占用 | 网络质量下降 |

### 防护措施

#### 1. SYN Cookies
**原理**：不为SYN请求分配资源，而是生成cookie验证
```
正常流程:
SYN → 分配资源 → SYN+ACK → 等待ACK

SYN Cookies:
SYN → 生成Cookie → SYN+ACK(含Cookie) → 验证ACK中的Cookie → 分配资源
```

**优点**：
- 不占用服务器资源
- 透明防护，不影响正常连接
- 有效防止资源耗尽

#### 2. 系统参数调优
```bash
# Linux系统参数优化
echo 1 > /proc/sys/net/ipv4/tcp_syncookies          # 启用SYN Cookies
echo 1024 > /proc/sys/net/ipv4/tcp_max_syn_backlog  # 增大SYN队列
echo 5 > /proc/sys/net/ipv4/tcp_syn_retries         # 减少SYN重试次数
echo 3 > /proc/sys/net/ipv4/tcp_synack_retries      # 减少SYN+ACK重试
```

#### 3. 防火墙规则
```bash
# iptables限制SYN包频率
iptables -A INPUT -p tcp --syn -m limit --limit 1/s --limit-burst 3 -j ACCEPT
iptables -A INPUT -p tcp --syn -j DROP

# 限制单IP连接数
iptables -A INPUT -p tcp --syn --dport 80 -m connlimit --connlimit-above 10 -j DROP
```

#### 4. 负载均衡和CDN
- **分散攻击**：多台服务器分担压力
- **流量清洗**：CDN过滤恶意流量
- **弹性扩容**：动态增加服务器资源

### 检测方法

#### 1. 系统监控
```bash
# 查看半连接状态
netstat -an | grep SYN_RECV | wc -l

# 查看连接状态统计
ss -ant | awk '{print $1}' | sort | uniq -c

# 监控SYN包数量
tcpdump -i eth0 'tcp[tcpflags] & tcp-syn != 0' | wc -l
```

#### 2. 性能指标
- **连接建立失败率**：正常<1%，攻击时>10%
- **响应时间**：正常<100ms，攻击时>1s
- **CPU使用率**：异常升高
- **内存使用率**：快速增长

### Go语言防护实现

#### 连接限制
```go
package main

import (
    "net"
    "sync"
    "time"
)

type ConnectionLimiter struct {
    connections map[string]int
    mutex       sync.RWMutex
    maxConn     int
}

func NewConnectionLimiter(maxConn int) *ConnectionLimiter {
    return &ConnectionLimiter{
        connections: make(map[string]int),
        maxConn:     maxConn,
    }
}

func (cl *ConnectionLimiter) Allow(ip string) bool {
    cl.mutex.Lock()
    defer cl.mutex.Unlock()

    if cl.connections[ip] >= cl.maxConn {
        return false
    }

    cl.connections[ip]++
    return true
}

func (cl *ConnectionLimiter) Release(ip string) {
    cl.mutex.Lock()
    defer cl.mutex.Unlock()

    if cl.connections[ip] > 0 {
        cl.connections[ip]--
    }
}
```

#### 速率限制
```go
import (
    "golang.org/x/time/rate"
    "sync"
)

type RateLimiter struct {
    limiters map[string]*rate.Limiter
    mutex    sync.RWMutex
    rate     rate.Limit
    burst    int
}

func NewRateLimiter(r rate.Limit, b int) *RateLimiter {
    return &RateLimiter{
        limiters: make(map[string]*rate.Limiter),
        rate:     r,
        burst:    b,
    }
}

func (rl *RateLimiter) Allow(ip string) bool {
    rl.mutex.Lock()
    defer rl.mutex.Unlock()

    limiter, exists := rl.limiters[ip]
    if !exists {
        limiter = rate.NewLimiter(rl.rate, rl.burst)
        rl.limiters[ip] = limiter
    }

    return limiter.Allow()
}
```

### 面试要点

**Q: SYN Flood攻击的原理是什么？**
A:
1. 攻击者伪造大量源IP发送SYN包
2. 服务器为每个SYN分配资源进入SYN_RCVD状态
3. 攻击者不回复ACK，导致半连接堆积
4. 服务器资源耗尽，无法处理新连接

**Q: 如何防护SYN Flood攻击？**
A:
1. **SYN Cookies**：不预分配资源，用cookie验证
2. **系统调优**：调整队列大小、超时时间
3. **防火墙**：限制连接频率和数量
4. **负载均衡**：分散攻击压力

**Q: SYN Cookies的工作原理？**
A:
1. 收到SYN时不分配资源
2. 生成包含连接信息的cookie作为序列号
3. 在SYN+ACK中发送cookie
4. 收到ACK时验证cookie，再分配资源

**Q: 如何检测SYN Flood攻击？**
A:
1. **监控半连接数量**：SYN_RECV状态异常增多
2. **连接成功率下降**：大量连接建立失败
3. **系统资源异常**：CPU、内存使用率飙升
4. **网络流量分析**：SYN包比例异常