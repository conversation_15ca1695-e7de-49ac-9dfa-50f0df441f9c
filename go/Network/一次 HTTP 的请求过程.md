## 一次完整的HTTP请求过程

### 请求流程概览

1. **DNS解析** → 域名转换为IP地址
2. **建立TCP连接** → 三次握手建立连接
3. **发送HTTP请求** → 构造并发送请求报文
4. **服务器处理** → 解析请求并处理业务逻辑
5. **返回HTTP响应** → 构造并发送响应报文
6. **浏览器处理响应** → 解析响应并渲染页面
7. **连接管理** → 根据协议版本决定是否保持连接

### 详细步骤

#### 1. DNS解析
- 浏览器缓存 → 系统缓存 → 路由器缓存 → ISP DNS → 根域名服务器
- 递归查询获取目标服务器IP地址

#### 2. TCP连接建立
- 三次握手：SYN → SYN+ACK → ACK
- 建立可靠的传输通道

#### 3. HTTP请求构造
```
GET /path HTTP/1.1
Host: example.com
User-Agent: Mozilla/5.0...
Accept: text/html,application/xhtml+xml
```

#### 4. 服务器处理
- 解析请求行、请求头、请求体
- 路由匹配、业务逻辑处理
- 数据库查询、文件读取等

#### 5. HTTP响应返回
```
HTTP/1.1 200 OK
Content-Type: text/html
Content-Length: 1234

<html>...</html>
```

#### 6. 浏览器渲染
- 解析HTML构建DOM树
- 解析CSS构建样式树
- 执行JavaScript
- 渲染页面

### 连接管理

**HTTP/1.0**：请求完成后立即关闭连接
**HTTP/1.1**：支持Keep-Alive持久连接
**HTTP/2**：多路复用，单连接并发处理多个请求

### 面试要点

**Q: 为什么需要三次握手？**
A: 确保双方收发能力正常，防止旧连接请求造成错误

**Q: DNS解析过程？**
A: 递归查询：本地缓存→DNS服务器→根域→顶级域→权威域

**Q: HTTP/1.1的优化？**
A: Keep-Alive、管道化、缓存控制、分块传输编码