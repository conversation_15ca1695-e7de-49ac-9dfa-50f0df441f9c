## HTTP长连接与短连接

### 基本概念对比

| 特性 | 短连接 | 长连接 |
|------|--------|--------|
| **连接方式** | 每次请求建立新连接 | 连接复用，保持打开 |
| **性能开销** | 高（频繁握手挥手） | 低（减少连接开销） |
| **资源占用** | 低（及时释放） | 高（持续占用） |
| **管理复杂度** | 简单 | 复杂（需处理超时） |
| **适用场景** | 低频请求、简单应用 | 高频请求、实时通信 |

### Go中的连接池配置

#### 关键参数说明
```go
transport := &http.Transport{
    MaxIdleConns:        100,              // 全局最大空闲连接数
    MaxIdleConnsPerHost: 10,               // 每个主机最大空闲连接数
    IdleConnTimeout:     90 * time.Second, // 空闲连接超时时间
    MaxConnsPerHost:     100,              // 每个主机最大连接数
    DisableKeepAlives:   false,            // 是否禁用Keep-Alive
}
```

#### 默认值陷阱
- **`MaxIdleConnsPerHost`默认值为2**：这是性能瓶颈的常见原因
- 高并发场景下必须调整此参数，否则连接无法有效复用

### 常见问题：TIME_WAIT过多

#### 问题现象
- 大量TIME_WAIT状态连接
- 连接频繁建立和关闭
- 每个TCP连接只处理少量HTTP请求

#### 根本原因
1. **连接池配置不当**：`MaxIdleConnsPerHost`过小
2. **Keep-Alive未生效**：服务端或客户端配置问题
3. **资源限制**：文件描述符不足

#### 解决方案
```go
// 优化的连接池配置
client := &http.Client{
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 100,              // 关键：增大此值
        IdleConnTimeout:     90 * time.Second,
        DisableKeepAlives:   false,
        MaxConnsPerHost:     100,
    },
    Timeout: 30 * time.Second,
}
```

### 最佳实践

#### 1. 连接池配置
- 根据并发量调整`MaxIdleConnsPerHost`
- 设置合理的`IdleConnTimeout`
- 监控连接池使用情况

#### 2. 系统层面优化
```bash
# 增加文件描述符限制
ulimit -n 65536

# 调整TCP参数（谨慎使用）
echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse
```

#### 3. 应用层面
- 正确关闭响应体：`defer resp.Body.Close()`
- 复用HTTP客户端实例
- 避免频繁创建新的Transport

### 监控指标

#### 关键监控点
- TIME_WAIT连接数量
- 连接池使用率
- 连接建立频率
- 响应时间分布

#### 诊断命令
```bash
# 查看连接状态
netstat -an | grep TIME_WAIT | wc -l

# 查看连接分布
ss -s

# 监控连接变化
watch -n 1 'netstat -an | grep :80 | wc -l'
```

### 面试要点

**Q: 什么情况下会产生大量TIME_WAIT？**
A:
1. 客户端主动关闭连接时进入TIME_WAIT状态
2. 连接池配置不当导致连接无法复用
3. Keep-Alive未生效，每次请求都建立新连接

**Q: 如何优化HTTP客户端性能？**
A:
1. 调整连接池参数，特别是`MaxIdleConnsPerHost`
2. 启用Keep-Alive并设置合理超时
3. 复用HTTP客户端实例
4. 监控和调优系统参数

**Q: Go默认的HTTP客户端有什么问题？**
A: `MaxIdleConnsPerHost`默认值为2，在高并发场景下会成为性能瓶颈