## TCP报文头部结构

### TCP头部格式

TCP头部固定长度为20字节，可通过选项字段扩展到最大60字节。

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|          源端口号              |          目的端口号            |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                        序列号                                  |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                        确认号                                  |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|  头长 |保留|U|A|P|R|S|F|            窗口大小                   |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|           校验和               |          紧急指针              |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                    选项 (可变长度)                             |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                             数据                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

### 字段详解

| 字段 | 长度 | 作用 | 说明 |
|------|------|------|------|
| **源端口** | 16位 | 发送方端口号 | 0-65535 |
| **目的端口** | 16位 | 接收方端口号 | 0-65535 |
| **序列号** | 32位 | 数据排序 | 标识发送的字节流位置 |
| **确认号** | 32位 | 确认接收 | 期望接收的下一个序列号 |
| **头长度** | 4位 | 头部长度 | 以4字节为单位(5-15) |
| **保留** | 3位 | 预留字段 | 必须为0 |
| **标志位** | 6位 | 控制信息 | URG/ACK/PSH/RST/SYN/FIN |
| **窗口大小** | 16位 | 流量控制 | 接收窗口大小 |
| **校验和** | 16位 | 错误检测 | 头部和数据的校验 |
| **紧急指针** | 16位 | 紧急数据 | URG=1时有效 |

### 标志位详解

#### 6个控制标志位
| 标志 | 全称 | 作用 | 使用场景 |
|------|------|------|----------|
| **URG** | Urgent | 紧急指针有效 | 紧急数据传输 |
| **ACK** | Acknowledgment | 确认号有效 | 除SYN外都设置 |
| **PSH** | Push | 立即推送数据 | 交互式应用 |
| **RST** | Reset | 重置连接 | 连接异常 |
| **SYN** | Synchronize | 同步序列号 | 建立连接 |
| **FIN** | Finish | 结束连接 | 关闭连接 |

#### 标志位组合
```
SYN=1, ACK=0  → 连接请求
SYN=1, ACK=1  → 连接确认
FIN=1, ACK=1  → 关闭请求
RST=1         → 强制关闭
ACK=1         → 数据确认
```

### 序列号和确认号

#### 序列号机制
```
发送数据: "Hello"
序列号: 1000
数据长度: 5字节
下一个序列号: 1005
```

#### 确认号机制
```
接收到序列号1000-1004的数据
确认号: 1005 (期望接收的下一个序列号)
```

#### 三次握手中的序列号
```
客户端: SYN, seq=x
服务端: SYN+ACK, seq=y, ack=x+1
客户端: ACK, seq=x+1, ack=y+1
```

### 窗口大小与流量控制

#### 窗口大小作用
- **流量控制**：防止发送方发送过快
- **缓冲区管理**：反映接收方可用缓冲区
- **性能优化**：影响传输效率

#### 窗口缩放选项
```
实际窗口 = 窗口字段 × 2^缩放因子
最大窗口 = 65535 × 2^14 = 1GB
```

### 常用TCP选项

| 选项 | 长度 | 作用 |
|------|------|------|
| **MSS** | 4字节 | 最大段大小 |
| **窗口缩放** | 3字节 | 扩大窗口大小 |
| **SACK** | 可变 | 选择性确认 |
| **时间戳** | 10字节 | RTT测量 |

### 校验和计算

#### 伪头部
```
源IP地址 (4字节)
目的IP地址 (4字节)
协议号 (1字节, TCP=6)
TCP长度 (2字节)
```

#### 校验和范围
- TCP头部
- TCP数据
- 伪头部

### 实际应用示例

#### 抓包分析
```bash
# 使用tcpdump抓取TCP包
tcpdump -i eth0 -nn tcp port 80

# 使用wireshark分析TCP头部
wireshark -i eth0 -f "tcp port 80"
```

#### Go语言解析TCP头部
```go
type TCPHeader struct {
    SrcPort    uint16
    DstPort    uint16
    SeqNum     uint32
    AckNum     uint32
    DataOffset uint8
    Flags      uint8
    Window     uint16
    Checksum   uint16
    UrgPtr     uint16
}
```

### 面试要点

**Q: TCP头部的固定长度是多少？**
A: 20字节，通过选项字段可扩展到最大60字节

**Q: TCP的6个标志位分别是什么？**
A: URG(紧急)、ACK(确认)、PSH(推送)、RST(重置)、SYN(同步)、FIN(结束)

**Q: 序列号和确认号的作用？**
A:
- **序列号**：标识发送数据的字节流位置，保证数据有序
- **确认号**：表示期望接收的下一个序列号，实现可靠传输

**Q: 窗口大小字段的作用？**
A: 用于流量控制，表示接收方当前可接收的数据量，防止发送方发送过快

**Q: 为什么需要校验和？**
A: 检测TCP头部和数据在传输过程中是否出现错误，保证数据完整性