## HTTP/2与HTTP/3特性对比

### HTTP版本演进

| 特性 | HTTP/1.1 | HTTP/2 | HTTP/3 |
|------|----------|--------|--------|
| **传输协议** | TCP | TCP | UDP (QUIC) |
| **多路复用** | 无 | 二进制帧多路复用 | 流级别多路复用 |
| **头部压缩** | 无 | HPACK | QPACK |
| **服务器推送** | 无 | 支持 | 支持 |
| **连接建立** | 多次握手 | 1次TCP握手 | 0-RTT/1-RTT |
| **队头阻塞** | 严重 | 应用层解决 | 完全解决 |

### HTTP/2核心特性

#### 1. 二进制分帧
**HTTP/1.1文本协议 → HTTP/2二进制协议**
```
HTTP/1.1:
GET /index.html HTTP/1.1\r\n
Host: example.com\r\n\r\n

HTTP/2:
[帧头][帧类型:HEADERS][流ID:1][头部数据]
[帧头][帧类型:DATA][流ID:1][响应数据]
```

#### 2. 多路复用
**单连接并发处理多个请求**
```
连接1:
流1: GET /css/style.css
流3: GET /js/app.js  
流5: GET /images/logo.png
流7: POST /api/data
```

#### 3. 头部压缩(HPACK)
**减少重复头部传输**
```
请求1: :method: GET, :path: /index.html, host: example.com
请求2: :method: GET, :path: /style.css, host: example.com
       ↓ HPACK压缩
请求2: :method: GET, :path: /style.css, [索引引用host]
```

#### 4. 服务器推送
**主动推送相关资源**
```
客户端请求: GET /index.html
服务器推送: 
- PUSH_PROMISE /style.css
- PUSH_PROMISE /app.js
- 发送 index.html
- 发送 style.css
- 发送 app.js
```

#### 5. 流优先级
**资源加载优先级控制**
```
优先级树:
HTML (权重256)
├── CSS (权重220)
├── JS (权重110) 
└── Images (权重32)
```

### HTTP/3核心特性

#### 1. QUIC协议基础
**基于UDP的可靠传输协议**
```
QUIC = UDP + 可靠性 + 加密 + 多路复用
```

#### 2. 连接建立优化
**0-RTT/1-RTT连接建立**
```
首次连接 (1-RTT):
客户端 → Initial包(TLS握手) → 服务端
客户端 ← Handshake包 ← 服务端
客户端 → 应用数据 → 服务端

后续连接 (0-RTT):
客户端 → 0-RTT数据 → 服务端
```

#### 3. 流级别多路复用
**彻底解决队头阻塞**
```
HTTP/2 (TCP):
流1阻塞 → 影响流2、流3

HTTP/3 (QUIC):
流1阻塞 → 不影响流2、流3
```

#### 4. 连接迁移
**网络切换时保持连接**
```
WiFi → 4G网络切换:
HTTP/2: 连接断开，重新建立
HTTP/3: 连接ID不变，无缝切换
```

#### 5. 改进的拥塞控制
**更精确的拥塞控制算法**
- **BBR算法**：基于带宽和RTT的拥塞控制
- **丢包恢复**：更快的丢包检测和恢复
- **RTT测量**：更准确的往返时间测量

### 性能对比

#### 延迟优化
```
连接建立时间:
HTTP/1.1: DNS + TCP握手 + TLS握手 = 3-RTT
HTTP/2:   DNS + TCP握手 + TLS握手 = 3-RTT  
HTTP/3:   DNS + QUIC握手 = 1-RTT (首次)
          DNS + 0-RTT = 0-RTT (后续)
```

#### 吞吐量提升
```
并发请求处理:
HTTP/1.1: 6个连接 × 1个请求 = 6个并发
HTTP/2:   1个连接 × 无限流 = 高并发
HTTP/3:   1个连接 × 无限流 + 无队头阻塞 = 更高并发
```

### 实际应用场景

#### HTTP/2适用场景
- **Web应用**：大量小资源的网站
- **API服务**：高频率的API调用
- **移动应用**：减少连接数，节省电量

#### HTTP/3适用场景
- **视频流媒体**：对延迟敏感的实时应用
- **移动网络**：网络不稳定的环境
- **IoT设备**：需要连接迁移的场景
- **游戏应用**：低延迟要求的实时游戏

### 部署考虑

#### HTTP/2部署
```nginx
# Nginx配置
server {
    listen 443 ssl http2;
    ssl_certificate cert.pem;
    ssl_certificate_key key.pem;
    
    # HTTP/2推送
    location / {
        http2_push /css/style.css;
        http2_push /js/app.js;
    }
}
```

#### HTTP/3部署
```nginx
# Nginx配置 (需要支持QUIC的版本)
server {
    listen 443 ssl http3 reuseport;
    listen 443 ssl http2;
    
    # 通告HTTP/3支持
    add_header Alt-Svc 'h3=":443"; ma=86400';
}
```

### 兼容性与回退

#### 协议协商
```
客户端支持: HTTP/1.1, HTTP/2, HTTP/3
服务端支持: HTTP/1.1, HTTP/2

协商结果: HTTP/2
```

#### 优雅降级
```
HTTP/3 → HTTP/2 → HTTP/1.1
QUIC不可用 → TCP连接
```

### Go语言支持

#### HTTP/2
```go
// Go自动支持HTTP/2 (需要HTTPS)
http.ListenAndServeTLS(":8443", "cert.pem", "key.pem", nil)

// 服务器推送
if pusher, ok := w.(http.Pusher); ok {
    pusher.Push("/style.css", nil)
}
```

#### HTTP/3
```go
// 使用quic-go库
import "github.com/quic-go/quic-go/http3"

client := &http.Client{
    Transport: &http3.RoundTripper{},
}
```

### 面试要点

**Q: HTTP/2相比HTTP/1.1有什么优势？**
A:
1. **多路复用**：单连接并发处理多个请求
2. **头部压缩**：HPACK减少重复头部传输
3. **服务器推送**：主动推送相关资源
4. **二进制协议**：更高效的解析

**Q: HTTP/3解决了什么问题？**
A:
1. **队头阻塞**：QUIC流级别多路复用彻底解决
2. **连接建立延迟**：0-RTT/1-RTT快速建立连接
3. **连接迁移**：网络切换时保持连接
4. **拥塞控制**：更精确的拥塞控制算法

**Q: 什么时候选择HTTP/3？**
A:
1. 对延迟敏感的应用（视频、游戏）
2. 移动网络环境（网络不稳定）
3. 需要连接迁移的场景
4. 高并发、高吞吐量要求

**Q: HTTP/2的服务器推送有什么注意事项？**
A:
1. 避免推送客户端已有的资源
2. 推送的资源应该是客户端即将需要的
3. 注意推送的资源大小和数量
4. 客户端可以拒绝推送的资源
