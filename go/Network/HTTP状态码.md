## HTTP状态码

### 状态码分类

HTTP状态码分为5类，用三位数字表示请求处理结果：

| 类别 | 范围 | 含义 |
|------|------|------|
| **1xx** | 100-199 | 信息性响应 |
| **2xx** | 200-299 | 成功响应 |
| **3xx** | 300-399 | 重定向 |
| **4xx** | 400-499 | 客户端错误 |
| **5xx** | 500-599 | 服务器错误 |

### 常见状态码详解

#### 2xx 成功状态码
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| **200 OK** | 请求成功 | GET、POST成功 |
| **201 Created** | 资源已创建 | POST创建资源 |
| **204 No Content** | 成功但无内容 | DELETE成功 |
| **206 Partial Content** | 部分内容 | 断点续传 |

#### 3xx 重定向状态码
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| **301 Moved Permanently** | 永久重定向 | 网站迁移 |
| **302 Found** | 临时重定向 | 临时跳转 |
| **304 Not Modified** | 未修改 | 缓存有效 |
| **307 Temporary Redirect** | 临时重定向(保持方法) | 维护页面 |

#### 4xx 客户端错误
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| **400 Bad Request** | 请求错误 | 参数格式错误 |
| **401 Unauthorized** | 未认证 | 需要登录 |
| **403 Forbidden** | 禁止访问 | 权限不足 |
| **404 Not Found** | 资源不存在 | 页面不存在 |
| **405 Method Not Allowed** | 方法不允许 | GET访问POST接口 |
| **409 Conflict** | 冲突 | 资源状态冲突 |
| **429 Too Many Requests** | 请求过多 | 限流触发 |

#### 5xx 服务器错误
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| **500 Internal Server Error** | 服务器内部错误 | 代码异常 |
| **502 Bad Gateway** | 网关错误 | 上游服务异常 |
| **503 Service Unavailable** | 服务不可用 | 服务器过载 |
| **504 Gateway Timeout** | 网关超时 | 上游服务超时 |

### 状态码使用原则

#### RESTful API设计
```
GET    /users/123    → 200 OK (成功)
                     → 404 Not Found (用户不存在)

POST   /users        → 201 Created (创建成功)
                     → 400 Bad Request (参数错误)

PUT    /users/123    → 200 OK (更新成功)
                     → 404 Not Found (用户不存在)

DELETE /users/123    → 204 No Content (删除成功)
                     → 404 Not Found (用户不存在)
```

#### 错误处理最佳实践
- **4xx**：客户端问题，返回错误详情
- **5xx**：服务器问题，记录日志，返回通用错误信息
- **重定向**：使用301/302，设置Location头

### 面试要点

**Q: 301和302的区别？**
A:
- **301**：永久重定向，搜索引擎会更新索引
- **302**：临时重定向，搜索引擎保持原URL

**Q: 401和403的区别？**
A:
- **401**：未认证，需要提供身份凭证
- **403**：已认证但权限不足，禁止访问

**Q: 502和504的区别？**
A:
- **502**：网关收到无效响应
- **504**：网关超时，未收到响应

**Q: 什么时候返回204？**
A: 操作成功但无需返回内容，如DELETE操作

**Q: 如何处理缓存相关状态码？**
A: 304配合If-Modified-Since/ETag实现条件请求